import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

// TODO: required to fix an error on clicking the selected location
class MapSelectionScreen extends StatefulWidget {
  final double initialLat;
  final double initialLng;
  final Function(List<double>) onLocationSelected;

  const MapSelectionScreen({super.key,
   required this.initialLat,
   required this.initialLng,
   required this.onLocationSelected});

  @override
  State<MapSelectionScreen> createState() => _MapSelectionScreenState();
}

class _MapSelectionScreenState extends State<MapSelectionScreen> {
  late double _lat;
  late double _lng;
  late Marker _marker;

  @override
  void initState() {
    super.initState();
    _lat = widget.initialLat;
    _lng = widget.initialLng;
    _marker = Marker(
      markerId: const MarkerId('marker'),
      position: LatLng(widget.initialLat, widget.initialLng),
      infoWindow: const InfoWindow(title: 'Selected Location'),
    );
  } 

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('select a location on map'),
      ),
      body: GoogleMap(
        initialCameraPosition: CameraPosition(
          target: LatLng(_lat, _lng),
          zoom: 15,
        ),
        markers: {_marker},
        onTap: (LatLng latLng) {
          setState(() {
            _marker = Marker(
              markerId: const MarkerId('marker'),
              position: latLng,
              infoWindow: const InfoWindow(title: 'Selected Location'),
            );
            _lat = latLng.latitude.toDouble();
            _lng = latLng.longitude.toDouble();
          });
        },
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          widget.onLocationSelected([_lat.toDouble(), _lng.toDouble()]);
        },
        child: const Icon(Icons.check),
      ),
    );
  }
}
