import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

String timeFormatter(DateTime? time) {
  if (time == null) return 'Loading...';
  final now = DateTime.now();
  final sameDay =
      time.day == now.day && time.month == now.month && time.year == now.year;
  final hour = time.hour % 12 == 0 ? 12 : time.hour % 12;
  return sameDay
      ? '$hour:${time.minute.toString().padLeft(2, '0')} ${time.hour < 12 ? 'AM' : 'PM'}'
      : '$hour:${time.minute.toString().padLeft(2, '0')} ${time.hour < 12 ? 'AM' : 'PM'} (${time.day}-${time.month})';
}

class CirculatingText extends StatefulWidget {
  final List<String> texts;
  final Duration duration;

  const CirculatingText({
    super.key,
    required this.texts,
    this.duration = const Duration(seconds: 3),
  });

  @override
  State<CirculatingText> createState() => _CirculatingTextState();
}

class _CirculatingTextState extends State<CirculatingText> {
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _animateText();
  }

  void _animateText() {
    Future.delayed(widget.duration, () {
      setState(() {
        _currentIndex = (_currentIndex + 1) % widget.texts.length;
      });
      _animateText();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      widget.texts[_currentIndex],
      style: GoogleFonts.mallanna(
        fontSize: 15,
        fontWeight: FontWeight.w400,
      ),
    );
  }
}

class GoodAndBadCircle extends StatelessWidget {
  final double good;
  final double bad;

  const GoodAndBadCircle({
    super.key,
    required this.good,
    required this.bad,
  });

  @override
  Widget build(BuildContext context) {
    double ratio = 0.8;

    return Container(
      height: 100*ratio,
      width: 100*ratio,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
      ),
      child: ClipOval(
        child: Row(
          children: [
            Container(color: Colors.green,width: good*ratio,),
            Container(color: Colors.red,width: bad*ratio,),
          ],
        ),
      ),
    );
  }
}
