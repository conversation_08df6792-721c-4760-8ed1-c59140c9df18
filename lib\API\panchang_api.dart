
import 'package:http/http.dart' as http;
import 'dart:convert';

import 'package:panchang_at_this_moment/data/language_conversion.dart';

class PanchangAPI {
  static Future<PanchangData> getPanchangData(
      DateTime dateTime, double lat, double lng, double alt) async {
    final url = Uri.parse(
        'https://panchang-krlb.onrender.com/panchang?date=${dateTime.day}-${dateTime.month}-${dateTime.year}&lat=$lat&lng=$lng&alt=$alt&tz=Asia%2FKolkata');
    final response = await http.get(url);

    if (response.statusCode == 200) {
      Map<String, dynamic> jsonData =
          jsonDecode(utf8.decode(response.body.codeUnits));

      return PanchangData.fromJson(jsonData);
    } else {
      throw Exception('Failed to load data');
    }
  }

  // static Future<PanchangData> getPanchangData(
  //     DateTime dateTime, double lat, double lng, double alt) async {
  //   String url = "https://hindu-calender-panchang.p.rapidapi.com/";
  //   Map<String, String> querystring = {
  //     "date":
  //         "${dateTime.day.toString().padLeft(2, '0')}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.year.toString()}",
  //     "lng": lng.toString(),
  //     "lat": lat.toString(),
  //     "tz": "Asia/Kolkata",
  //     "alt": alt.toString()
  //   };
  //   Map<String, String> headers = {
  //     "x-rapidapi-key": "**************************************************",
  //     "x-rapidapi-host": "hindu-calender-panchang.p.rapidapi.com"
  //   };

  //   final response = await http.get(
  //       Uri.parse(url).replace(queryParameters: querystring),
  //       headers: headers);

  //   if (response.statusCode == 200) {
  //     final jsonData = jsonDecode(response.body);
  //     return PanchangData.fromJson(jsonData);
  //   } else {
  //     throw Exception('Failed to load panchang data');
  //   }
  // }
}

class PanchangSomeTimeItem {
  final DateTime start;
  final DateTime end;

  PanchangSomeTimeItem({required this.start, required this.end});
}

PanchangSomeTimeItem parsePanchangSomeItem(data) {
  return PanchangSomeTimeItem(
    start: DateTime.fromMillisecondsSinceEpoch(
        (data['start']['timestamp'] * 1000).toInt()),
    end: DateTime.fromMillisecondsSinceEpoch(
        (data['end']['timestamp'] * 1000).toInt()),
  );
}

class PanchangDataForThreeDays {
  final PanchangData previousDay;
  final PanchangData currentDay;
  final PanchangData nextDay;

  PanchangDataForThreeDays({
    required this.previousDay,
    required this.currentDay,
    required this.nextDay,
  });
}

class PanchangAlwaysChangeItem {
  final String name;
  final DateTime start;
  final DateTime end;

  PanchangAlwaysChangeItem(
      {required this.name, required this.start, required this.end});
}

class PanchangData {
  final DateTime sunrise;
  final DateTime sunset;
  final DateTime moonrise;
  final DateTime moonset;
  final String weekday;
  final String maasa;
  final String samvatsara;
  final List<PanchangAlwaysChangeItem> nakshatraList;
  final List<PanchangAlwaysChangeItem> tithiPakshaList;
  final List<PanchangAlwaysChangeItem> nakshatraPadaList;
  final List<PanchangAlwaysChangeItem> yogaList;
  final List<PanchangAlwaysChangeItem> karanaList;
  final List<PanchangAlwaysChangeItem> suryaNakshatraList;
  final List<PanchangAlwaysChangeItem> suryaPadaList;
  final List<PanchangAlwaysChangeItem> lunarRaasiList;
  final List<PanchangAlwaysChangeItem> solarRaasiList;
  final PanchangSomeTimeItem bramhaMuhrat;
  final PanchangSomeTimeItem? abhijit;
  final PanchangSomeTimeItem godhuli;
  final PanchangSomeTimeItem pratahSandhya;
  final PanchangSomeTimeItem vijayMuhurat;
  final PanchangSomeTimeItem sayahnaSandhya;
  final PanchangSomeTimeItem nishitaMuhurta;
  final PanchangSomeTimeItem rahuKal;
  final PanchangSomeTimeItem gulikaiKal;
  final PanchangSomeTimeItem yamaganda;
  final List<PanchangSomeTimeItem> durMuhurtam;
  final List<PanchangSomeTimeItem> varjyam;
  final List<PanchangSomeTimeItem> amritKal;

  PanchangData({
    required this.sunrise,
    required this.sunset,
    required this.moonrise,
    required this.moonset,
    required this.maasa,
    required this.samvatsara,
    required this.weekday,
    required this.nakshatraList,
    required this.tithiPakshaList,
    required this.nakshatraPadaList,
    required this.yogaList,
    required this.karanaList,
    required this.suryaNakshatraList,
    required this.suryaPadaList,
    required this.lunarRaasiList,
    required this.solarRaasiList,
    required this.bramhaMuhrat,
    this.abhijit,
    required this.godhuli,
    required this.pratahSandhya,
    required this.vijayMuhurat,
    required this.sayahnaSandhya,
    required this.nishitaMuhurta,
    required this.rahuKal,
    required this.gulikaiKal,
    required this.yamaganda,
    required this.durMuhurtam,
    required this.varjyam,
    required this.amritKal,
  });

  PanchangData translate(String lang) {
    return copyWith(
      nakshatraList: nakshatraList.map((e) {
        return PanchangAlwaysChangeItem(
          start: e.start,
          end: e.end,
          name: (nakshatraNames[e.name]?[lang] != null)
              ? nakshatraNames[e.name]![lang]!
              : e.name,
        );
      }).toList(),
      suryaNakshatraList: suryaNakshatraList.map((e) {
        return PanchangAlwaysChangeItem(
          start: e.start,
          end: e.end,
          name: (nakshatraNames[e.name]?[lang] != null)
              ? nakshatraNames[e.name]![lang]!
              : e.name,
        );
      }).toList(),
      nakshatraPadaList: nakshatraPadaList.map((e) {
        final splitName = e.name.split(' ');
        return PanchangAlwaysChangeItem(
          start: e.start,
          end: e.end,
          name: (nakshatraNames[splitName[0]]?[lang] != null)
              ? '${nakshatraNames[splitName[0]]![lang]!} ${splitName.sublist(1).join(" ")}'
              : e.name,
        );
      }).toList(),
      suryaPadaList: suryaPadaList.map((e) {
        final splitName = e.name.split(' ');
        return PanchangAlwaysChangeItem(
          start: e.start,
          end: e.end,
          name: (nakshatraNames[splitName[0]]?[lang] != null)
              ? '${nakshatraNames[splitName[0]]![lang]!} ${splitName.sublist(1).join(" ")}'
              : e.name,
        );
      }).toList(),
      lunarRaasiList: lunarRaasiList.map((e) {
        return PanchangAlwaysChangeItem(
          start: e.start,
          end: e.end,
          name: (raashiNames[e.name]?[lang] != null)
              ? raashiNames[e.name]![lang]!
              : e.name,
        );
      }).toList(),
      solarRaasiList: solarRaasiList.map((e) {
        return PanchangAlwaysChangeItem(
          start: e.start,
          end: e.end,
          name: (raashiNames[e.name]?[lang] != null)
              ? raashiNames[e.name]![lang]!
              : e.name,
        );
      }).toList(),
      karanaList: karanaList.map((e) {
        return PanchangAlwaysChangeItem(
          start: e.start,
          end: e.end,
          name: (karanaTitles[e.name]?[lang] != null)
              ? karanaTitles[e.name]![lang]!
              : e.name,
        );
      }).toList(),
      yogaList: yogaList.map((e) {
        return PanchangAlwaysChangeItem(
          start: e.start,
          end: e.end,
          name: (yogaTitles[e.name]?[lang] != null)
              ? yogaTitles[e.name]![lang]!
              : e.name,
        );
      }).toList(),
      weekday: (weekDaysTrans[weekday]?[lang] != null)
          ? weekDaysTrans[weekday]![lang]!
          : weekday,
      maasa: monthNames[maasa]?[lang] ?? maasa,
      samvatsara: yearNames[samvatsara]?[lang] ?? samvatsara,
      tithiPakshaList: tithiPakshaList.map(
        (e) {
          final splitName = e.name.split(' : ');
          return PanchangAlwaysChangeItem(
            start: e.start,
            end: e.end,
            name: (tithiNames[splitName[0]]?[lang] != null && pakshaNames[splitName[1]]?[lang] != null)
                ? '${tithiNames[splitName[0]]![lang]!} : ${pakshaNames[splitName[1]]![lang]!}'
                : e.name,
          );
        }
      ).toList(),
    );
  }

  PanchangData copyWith({
    DateTime? sunrise,
    DateTime? sunset,
    DateTime? moonrise,
    DateTime? moonset,
    String? maasa,
    String? samvatsara,
    String? weekday,
    List<PanchangAlwaysChangeItem>? nakshatraList,
    List<PanchangAlwaysChangeItem>? tithiPakshaList,
    List<PanchangAlwaysChangeItem>? nakshatraPadaList,
    List<PanchangAlwaysChangeItem>? yogaList,
    List<PanchangAlwaysChangeItem>? karanaList,
    List<PanchangAlwaysChangeItem>? suryaNakshatraList,
    List<PanchangAlwaysChangeItem>? suryaPadaList,
    List<PanchangAlwaysChangeItem>? lunarRaasiList,
    List<PanchangAlwaysChangeItem>? solarRaasiList,
    PanchangSomeTimeItem? bramhaMuhrat,
    PanchangSomeTimeItem? abhijit,
    PanchangSomeTimeItem? godhuli,
    PanchangSomeTimeItem? pratahSandhya,
    PanchangSomeTimeItem? vijayMuhurat,
    PanchangSomeTimeItem? sayahnaSandhya,
    PanchangSomeTimeItem? nishitaMuhurta,
    PanchangSomeTimeItem? rahuKal,
    PanchangSomeTimeItem? gulikaiKal,
    PanchangSomeTimeItem? yamaganda,
    List<PanchangSomeTimeItem>? durMuhurtam,
    List<PanchangSomeTimeItem>? varjyam,
    List<PanchangSomeTimeItem>? amritKal,
  }) {
    return PanchangData(
      sunrise: sunrise ?? this.sunrise,
      sunset: sunset ?? this.sunset,
      moonrise: moonrise ?? this.moonrise,
      moonset: moonset ?? this.moonset,
      maasa: maasa ?? this.maasa,
      samvatsara: samvatsara ?? this.samvatsara,
      weekday: weekday ?? this.weekday,
      nakshatraList: nakshatraList ?? this.nakshatraList,
      tithiPakshaList: tithiPakshaList ?? this.tithiPakshaList,
      nakshatraPadaList: nakshatraPadaList ?? this.nakshatraPadaList,
      yogaList: yogaList ?? this.yogaList,
      karanaList: karanaList ?? this.karanaList,
      suryaNakshatraList: suryaNakshatraList ?? this.suryaNakshatraList,
      suryaPadaList: suryaPadaList ?? this.suryaPadaList,
      lunarRaasiList: lunarRaasiList ?? this.lunarRaasiList,
      solarRaasiList: solarRaasiList ?? this.solarRaasiList,
      bramhaMuhrat: bramhaMuhrat ?? this.bramhaMuhrat,
      abhijit: abhijit ?? this.abhijit,
      godhuli: godhuli ?? this.godhuli,
      pratahSandhya: pratahSandhya ?? this.pratahSandhya,
      vijayMuhurat: vijayMuhurat ?? this.vijayMuhurat,
      sayahnaSandhya: sayahnaSandhya ?? this.sayahnaSandhya,
      nishitaMuhurta: nishitaMuhurta ?? this.nishitaMuhurta,
      rahuKal: rahuKal ?? this.rahuKal,
      gulikaiKal: gulikaiKal ?? this.gulikaiKal,
      yamaganda: yamaganda ?? this.yamaganda,
      durMuhurtam: durMuhurtam ?? this.durMuhurtam,
      varjyam: varjyam ?? this.varjyam,
      amritKal: amritKal ?? this.amritKal,
    );
  }

  factory PanchangData.fromJson(Map<String, dynamic> json) {
    List<PanchangAlwaysChangeItem> nakshatraList = [];
    json['nakshatra'].forEach((key, value) {
      nakshatraList.add(PanchangAlwaysChangeItem(
        name: key,
        start: DateTime.fromMillisecondsSinceEpoch(
            (value['start']['timestamp'] * 1000).toInt()),
        end: DateTime.fromMillisecondsSinceEpoch(
            (value['end']['timestamp'] * 1000).toInt()),
      ));
    });

    List<PanchangAlwaysChangeItem> tithiPakshaList = [];
    json['tithiPaksha'].forEach((key, value) {
      tithiPakshaList.add(PanchangAlwaysChangeItem(
        name: key,
        start: DateTime.fromMillisecondsSinceEpoch(
            (value['start']['timestamp'] * 1000).toInt()),
        end: DateTime.fromMillisecondsSinceEpoch(
            (value['end']['timestamp'] * 1000).toInt()),
      ));
    });

    List<PanchangAlwaysChangeItem> nakshatraPadaList = [];
    json['nakshatraPada'].forEach((key, value) {
      nakshatraPadaList.add(PanchangAlwaysChangeItem(
        name: key,
        start: DateTime.fromMillisecondsSinceEpoch(
            (value['start']['timestamp'] * 1000).toInt()),
        end: DateTime.fromMillisecondsSinceEpoch(
            (value['end']['timestamp'] * 1000).toInt()),
      ));
    });

    List<PanchangAlwaysChangeItem> yogaList = [];
    json['yoga'].forEach((key, value) {
      yogaList.add(PanchangAlwaysChangeItem(
        name: key,
        start: DateTime.fromMillisecondsSinceEpoch(
            (value['start']['timestamp'] * 1000).toInt()),
        end: DateTime.fromMillisecondsSinceEpoch(
            (value['end']['timestamp'] * 1000).toInt()),
      ));
    });

    List<PanchangAlwaysChangeItem> karanaList = [];
    json['karana'].forEach((key, value) {
      karanaList.add(PanchangAlwaysChangeItem(
        name: key,
        start: DateTime.fromMillisecondsSinceEpoch(
            (value['start']['timestamp'] * 1000).toInt()),
        end: DateTime.fromMillisecondsSinceEpoch(
            (value['end']['timestamp'] * 1000).toInt()),
      ));
    });

    List<PanchangAlwaysChangeItem> suryaNakshatraList = [];
    json['suryaNakshatra'].forEach((key, value) {
      suryaNakshatraList.add(PanchangAlwaysChangeItem(
        name: key,
        start: DateTime.fromMillisecondsSinceEpoch(
            (value['start']['timestamp'] * 1000).toInt()),
        end: DateTime.fromMillisecondsSinceEpoch(
            (value['end']['timestamp'] * 1000).toInt()),
      ));
    });

    List<PanchangAlwaysChangeItem> suryaPadaList = [];
    json['suryaPada'].forEach((key, value) {
      suryaPadaList.add(PanchangAlwaysChangeItem(
        name: key,
        start: DateTime.fromMillisecondsSinceEpoch(
            (value['start']['timestamp'] * 1000).toInt()),
        end: DateTime.fromMillisecondsSinceEpoch(
            (value['end']['timestamp'] * 1000).toInt()),
      ));
    });

    List<PanchangAlwaysChangeItem> lunarRaasiList = [];
    json['lunarRaasi'].forEach((key, value) {
      lunarRaasiList.add(PanchangAlwaysChangeItem(
        name: key,
        start: DateTime.fromMillisecondsSinceEpoch(
            (value['start']['timestamp'] * 1000).toInt()),
        end: DateTime.fromMillisecondsSinceEpoch(
            (value['end']['timestamp'] * 1000).toInt()),
      ));
    });

    List<PanchangAlwaysChangeItem> solarRaasiList = [];
    json['solarRaasi'].forEach((key, value) {
      solarRaasiList.add(PanchangAlwaysChangeItem(
        name: key,
        start: DateTime.fromMillisecondsSinceEpoch(
            (value['start']['timestamp'] * 1000).toInt()),
        end: DateTime.fromMillisecondsSinceEpoch(
            (value['end']['timestamp'] * 1000).toInt()),
      ));
    });

    return PanchangData(
      sunrise: DateTime.fromMillisecondsSinceEpoch(
          (json['sunrise']['timestamp'] * 1000).toInt()),
      sunset: DateTime.fromMillisecondsSinceEpoch(
          (json['sunset']['timestamp'] * 1000).toInt()),
      moonrise: DateTime.fromMillisecondsSinceEpoch(
          (json['moonrise']['timestamp'] * 1000).toInt()),
      moonset: DateTime.fromMillisecondsSinceEpoch(
          (json['moonset']['timestamp'] * 1000).toInt()),
      maasa: json['maasa'],
      samvatsara: json['samvatsara'],
      weekday: [
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday'
      ][json['weekday']],
      nakshatraList: nakshatraList,
      tithiPakshaList: tithiPakshaList,
      nakshatraPadaList: nakshatraPadaList,
      yogaList: yogaList,
      karanaList: karanaList,
      suryaNakshatraList: suryaNakshatraList,
      suryaPadaList: suryaPadaList,
      lunarRaasiList: lunarRaasiList,
      solarRaasiList: solarRaasiList,
      bramhaMuhrat: parsePanchangSomeItem(json['bramhaMuhrat']),
      abhijit: json['abhijit'] != null
          ? parsePanchangSomeItem(json['abhijit'])
          : null,
      godhuli: parsePanchangSomeItem(json['godhuli']),
      pratahSandhya: parsePanchangSomeItem(json['pratahSandhya']),
      vijayMuhurat: parsePanchangSomeItem(json['vijayMuhurat']),
      sayahnaSandhya: parsePanchangSomeItem(json['sayahnaSandhya']),
      nishitaMuhurta: parsePanchangSomeItem(json['nishitaMuhurta']),
      rahuKal: parsePanchangSomeItem(json['rahuKal']),
      gulikaiKal: parsePanchangSomeItem(json['gulikaiKal']),
      yamaganda: parsePanchangSomeItem(json['yamaganda']),
      durMuhurtam: json['durMuhurtam']
          .map((item) => parsePanchangSomeItem(item))
          .toList()
          .cast<PanchangSomeTimeItem>(),
      varjyam: json['varjyam']
          .map((item) => parsePanchangSomeItem(item))
          .toList()
          .cast<PanchangSomeTimeItem>(),
      amritKal: json['amritKal']
          .map((item) => parsePanchangSomeItem(item))
          .toList()
          .cast<PanchangSomeTimeItem>(),
    );
  }
}
