import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class TimeControlWidget extends StatelessWidget {
  final DateTime currentTime;
  final bool isPlaying;
  final VoidCallback onPlayPause;
  final VoidCallback onReset;
  final Function(DateTime)? onTimeChanged;

  const TimeControlWidget({
    super.key,
    required this.currentTime,
    required this.isPlaying,
    required this.onPlayPause,
    required this.onReset,
    this.onTimeChanged,
  });

  String _getLocalizedDayName(BuildContext context, DateTime dateTime) {
    final locale = Localizations.localeOf(context);
    final dayFormat = DateFormat.EEEE(locale.toString());
    return dayFormat.format(dateTime);
  }

  Future<void> _selectTime(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: currentTime,
      firstDate: DateTime(1600),
      lastDate: DateTime(2400),
    );

    if (picked != null) {
      final TimeOfDay? timeOfDay = await showTimePicker(
        // ignore: use_build_context_synchronously
        context: context,
        initialTime: TimeOfDay.fromDateTime(currentTime),
      );

      if (timeOfDay != null) {
        final newDateTime = DateTime(
          picked.year,
          picked.month,
          picked.day,
          timeOfDay.hour,
          timeOfDay.minute,
        );
        onTimeChanged?.call(newDateTime);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(left:12,right: 10, bottom: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Row(
              children: [
                GestureDetector(
                  onTap: onTimeChanged != null ? () => _selectTime(context) : null,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF3E5AB),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.access_time_rounded,
                      color: Color(0xFF6B4E3D),
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        DateFormat('dd-MM-yyyy HH:mm:ss').format(currentTime),
                        style: Theme.of(context).textTheme.displayMedium?.copyWith(
                          color: Colors.white ,
                          fontWeight: FontWeight.w600,
                          fontSize: 18,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 1),
                      Text(
                        _getLocalizedDayName(context, currentTime),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: isPlaying ? const Color(0xFFFFEBEE) : const Color(0xFFE8F5E8),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: IconButton(
                  constraints: const BoxConstraints(minWidth: 48, minHeight: 48),
                  padding: const EdgeInsets.all(10),
                  onPressed: onPlayPause,
                  icon: Icon(
                    isPlaying
                        ? Icons.pause_circle_filled_rounded
                        : Icons.play_circle_fill_rounded,
                    color: isPlaying ? const Color(0xFFD32F2F) : const Color(0xFF388E3C),
                    size: 28,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFFF3E5AB),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: IconButton(
                  constraints: const BoxConstraints(minWidth: 48, minHeight: 48),
                  padding: const EdgeInsets.all(10),
                  icon: const Icon(
                    Icons.refresh_rounded,
                    color: Color(0xFF6B4E3D),
                    size: 26,
                  ),
                  onPressed: onReset,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
