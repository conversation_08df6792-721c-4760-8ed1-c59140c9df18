import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class PanchangNakshatraCard extends StatelessWidget {
  final Color color;
  final IconData icon;
  final String title;
  final List<Map<String, String>> infoRows;
  final Color? infoBgColor;

  const PanchangNakshatraCard({
    super.key,
    required this.color,
    required this.icon,
    required this.title,
    required this.infoRows,
    this.infoBgColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium!.copyWith(
                    fontSize: 18,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8,horizontal: 6),
            decoration: BoxDecoration(
              color: infoBgColor ?? color.withValues(alpha: 0.05),
              // borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                for (int i = 0; i < infoRows.length; i++) ...[
                  _buildNakshatraInfoRow(
                    label: infoRows[i]['label'] ?? '',
                    value: infoRows[i]['value'] ?? '',
                    context: context,
                  ),
                  if (i < infoRows.length - 1) const Divider(height: 20),
                ]
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNakshatraInfoRow({required String label, required String value, required BuildContext context}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: GoogleFonts.peddana(
              fontWeight: FontWeight.w500,
              fontSize: 15,
              color: const Color(0xFF8D6E63),
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: Colors.black,
            ),
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }
}
