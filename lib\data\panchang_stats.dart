import 'package:flutter_gen/gen_l10n/app_localizations.dart';

Map<String, Map<String, dynamic>> getPanchangStats(context) {
  return {
    "bramhaMuhrat": {
      "isPositive": true,
      "displayMessage": AppLocalizations.of(context)!.bramhaMuhratMessage,
      "score": 5
    },
    "abhijit": {
      "isPositive": true,
      "displayMessage": AppLocalizations.of(context)!.abhijitMessage,
      "score": 4.5
    },
    "godhuli": {
      "isPositive": true,
      "displayMessage": AppLocalizations.of(context)!.godhuliMessage,
      "score": 4
    },
    "pratahSandhya": {
      "isPositive": true,
      "displayMessage": AppLocalizations.of(context)!.pratahSandhyaMessage,
      "score": 4
    },
    "vijayMuhurat": {
      "isPositive": true,
      "displayMessage": AppLocalizations.of(context)!.vijayMuhuratMessage,
      "score": 5
    },
    "sayahnaSandhya": {
      "isPositive": true,
      "displayMessage": AppLocalizations.of(context)!.sayahnaSandhyaMessage,
      "score": 3.5
    },
    "nishitaMuhurta": {
      "isPositive": true,
      "displayMessage": AppLocalizations.of(context)!.nishitaMuhurtaMessage,
      "score": 3
    },
    "rahuKal": {
      "isPositive": false,
      "displayMessage": AppLocalizations.of(context)!.rahuKalMessage,
      "score": -4.5
    },
    "gulikaiKal": {
      "isPositive": false,
      "displayMessage": AppLocalizations.of(context)!.gulikaiKalMessage,
      "score": -4
    },
    "yamaganda": {
      "isPositive": false,
      "displayMessage": AppLocalizations.of(context)!.yamagandaMessage,
      "score": -4.5
    },
    "durMuhurtam": {
      "isPositive": false,
      "displayMessage": AppLocalizations.of(context)!.durMuhurtamMessage,
      "score": -5
    },
    "varjyam": {
      "isPositive": false,
      "displayMessage": AppLocalizations.of(context)!.varjyamMessage,
      "score": -4
    },
    "amritKal": {
      "isPositive": true,
      "displayMessage": AppLocalizations.of(context)!.amritKalMessage,
      "score": 5
    }
  };
}
