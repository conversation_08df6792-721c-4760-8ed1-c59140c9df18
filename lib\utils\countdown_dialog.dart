import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class CountDownItem {
  final DateTime startTime;
  final DateTime endTime;
  final String title;

  CountDownItem({required this.endTime, required this.title, required this.startTime});
}

class CountDownWithTimeDialog extends StatefulWidget {
  final Widget child;
  final DateTime currentTime;
  final List<CountDownItem> countDownItems;
  final bool showDateFormattedTime;
  final bool showMinutesFormattedTime;

  const CountDownWithTimeDialog({
    super.key,
    required this.child,
    required this.countDownItems,
    required this.currentTime,
    this.showDateFormattedTime = false,
    this.showMinutesFormattedTime = false,
  });

  @override
  State<CountDownWithTimeDialog> createState() =>
      _CountDownWithTimeDialogState();
}

class _CountDownWithTimeDialogState extends State<CountDownWithTimeDialog> {
  String _formatDuration(Duration duration) {
    int hours = duration.inHours;
    int minutes = duration.inMinutes;
    int seconds = duration.inSeconds % 60;
    int days = duration.inDays;

    if (widget.showMinutesFormattedTime) {
      return '${minutes.toString().padLeft(2, '0')} mins ${seconds.toString().padLeft(2, '0')} secs';
    } else if (widget.showDateFormattedTime) {
      return '${days.toString()} days ${(hours % 24).toString().padLeft(2, '0')} hrs ${(minutes % 60).toString().padLeft(2, '0')} mins ${seconds.toString().padLeft(2, '0')} secs';
    } else {
      return '${hours.toString().padLeft(2, '0')} hrs ${(minutes % 60).toString().padLeft(2, '0')} mins ${seconds.toString().padLeft(2, '0')} secs';
    }
  }

  String _formatEndTime(DateTime endTime) {
    final now = widget.currentTime;
    final isSameDay = DateUtils.isSameDay(endTime, now);
    final timeFormat = DateFormat.jm();

    return isSameDay
        ? timeFormat.format(endTime)
        : DateFormat('dd-MM-yyyy').add_jm().format(endTime);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showDialog(
          context: context,
          builder: (context) {
            return AlertDialog(
              title: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Row(
                    children: <Widget>[
                      Icon(Icons.timer,
                          color: Theme.of(context).colorScheme.primary,
                          size: 24),
                      const SizedBox(width: 8),
                      Text('Panchang Timer',
                          style: Theme.of(context)
                              .textTheme
                              .titleMedium!
                              .copyWith(fontSize: 18)),
                    ],
                  ),
                  GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Container(
                          alignment: Alignment.topRight,
                          child: const Icon(
                            Icons.cancel,
                            color: Colors.red,
                          ))),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  for (CountDownItem item in widget.countDownItems)
                    StreamBuilder(
                      stream:
                          Stream.periodic(const Duration(seconds: 1), (i) => i),
                      builder: (context, snapshot) {
                        return item.endTime.isAfter(widget.currentTime)
                            ? SizedBox(
                                width: double.infinity,
                                child: Card(
                                  elevation: 8,
                                  child: Container(
                                    padding: const EdgeInsets.all(12.0),
                                    margin: const EdgeInsets.only(bottom: 12.0),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: <Widget>[
                                        Text(
                                          item.title,
                                          style: const TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                        const SizedBox(height: 12),
                                        Text(
                                          '${AppLocalizations.of(context)!.start} : '
                                          '${_formatEndTime(item.startTime)}',
                                          style: const TextStyle(
                                            fontSize: 14,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          '${AppLocalizations.of(context)!.end} : '
                                          '${_formatEndTime(item.endTime)}',
                                          style: const TextStyle(
                                            fontSize: 14,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          '${AppLocalizations.of(context)!.timeLeft} : '
                                          '${_formatDuration(item.endTime.difference(widget.currentTime))}',
                                          style: const TextStyle(
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              )
                            : const SizedBox.shrink();
                      },
                    ),
                ],
              ),
            );
          },
        );
      },
      child: widget.child,
    );
  }
}
